// Enhanced AI Response System with Dynamic Knowledge Integration
import { KnowledgeScraper, KnowledgeChunk } from './knowledge-scraper';

export interface AIResponseContext {
  userQuery: string;
  conversationHistory: string[];
  userContext?: {
    name?: string;
    email?: string;
    previousTopics?: string[];
  };
}

export interface AIResponse {
  content: string;
  confidence: number;
  sources: string[];
  suggestedActions?: string[];
}

export class EnhancedAIResponder {
  private knowledgeScraper: KnowledgeScraper;
  private apiKey: string;

  constructor(apiKey: string) {
    this.knowledgeScraper = KnowledgeScraper.getInstance();
    this.apiKey = apiKey;
  }

  // Generate enhanced AI response using dynamic knowledge
  async generateResponse(context: AIResponseContext): Promise<AIResponse> {
    try {
      // Search relevant knowledge chunks
      const relevantChunks = this.knowledgeScraper.searchKnowledge(context.userQuery, 3);
      
      if (relevantChunks.length === 0) {
        return this.generateFallbackResponse(context);
      }

      // Build context-aware prompt
      const prompt = this.buildEnhancedPrompt(context, relevantChunks);
      
      // Get AI response
      const aiResponse = await this.callOpenAI(prompt);
      
      return {
        content: aiResponse,
        confidence: this.calculateConfidence(relevantChunks),
        sources: relevantChunks.map(chunk => chunk.source),
        suggestedActions: this.generateSuggestedActions(context.userQuery, relevantChunks)
      };

    } catch (error) {
      console.error('Enhanced AI Response Error:', error);
      return this.generateFallbackResponse(context);
    }
  }

  // Build comprehensive prompt with dynamic knowledge
  private buildEnhancedPrompt(context: AIResponseContext, chunks: KnowledgeChunk[]): string {
    const knowledgeContext = chunks.map(chunk => 
      `Source: ${chunk.source}\nContent: ${chunk.content}`
    ).join('\n\n');

    const conversationContext = context.conversationHistory.length > 0 
      ? `\nCONVERSATION HISTORY:\n${context.conversationHistory.slice(-4).join('\n')}`
      : '';

    return `You are UpZera's intelligent AI assistant. You provide helpful, accurate, and engaging responses about UpZera's services and capabilities.

COMPANY CONTEXT:
UpZera is a digital solutions company based in Eindhoven, Netherlands, founded in 2025. We specialize in:
- Full-Stack Web Development (starting from €350)
- AI Chatbots & Lead Generation (starting from €500)
- Custom digital solutions for startups and growing businesses

RELEVANT KNOWLEDGE BASE:
${knowledgeContext}

USER QUERY: "${context.userQuery}"
${conversationContext}

RESPONSE GUIDELINES:
1. Use the knowledge base as your primary source of truth
2. Be conversational, helpful, and professional
3. Keep responses concise but informative (2-3 sentences max)
4. If discussing services, mention relevant pricing when appropriate
5. Provide informative content without asking follow-up questions
6. Use emojis sparingly but effectively
7. If the query is outside UpZera's scope, politely redirect to our services

FORMATTING REQUIREMENTS:
- Structure your response with proper spacing for readability
- When mentioning multiple services or prices, separate them with line breaks
- Use natural sentence breaks to create digestible chunks of information
- For example: "Our pricing is project-based and tailored to your specific needs. For example, landing pages start from €350, full websites range from €1,400 to €2,400, and chatbot setups typically cost between €500 and €1,200. After a free consultation, you'll receive a personalized quote based on your goals and project scope!"

IMPORTANT: Base your response primarily on the provided knowledge base. If the knowledge base doesn't contain relevant information, acknowledge this and provide what information you can. DO NOT ask follow-up questions - just provide the information requested.

Response:`;
  }

  // Call OpenAI API with enhanced prompt
  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 200,
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`OpenAI API Error: ${data.error.message}`);
    }

    return data.choices[0]?.message?.content?.trim() || 
           "I'd be happy to help! Let me connect you with our team for personalized assistance.";
  }

  // Calculate confidence based on knowledge relevance
  private calculateConfidence(chunks: KnowledgeChunk[]): number {
    if (chunks.length === 0) return 0.1;
    
    const avgScore = chunks.reduce((sum, chunk) => sum + (chunk.relevanceScore || 0), 0) / chunks.length;
    
    // Convert to confidence score (0-1)
    return Math.min(0.9, Math.max(0.3, avgScore / 10));
  }

  // Generate suggested actions based on query and knowledge
  private generateSuggestedActions(query: string, chunks: KnowledgeChunk[]): string[] {
    const actions: string[] = [];
    const queryLower = query.toLowerCase();

    // Service-specific suggestions
    if (queryLower.includes('web') || queryLower.includes('website')) {
      actions.push('Learn more about our web development services');
      actions.push('See our web development pricing');
    }

    if (queryLower.includes('chatbot') || queryLower.includes('ai')) {
      actions.push('Explore our AI chatbot solutions');
      actions.push('Get chatbot pricing information');
    }

    if (queryLower.includes('price') || queryLower.includes('cost')) {
      actions.push('Schedule a free consultation for detailed pricing');
      actions.push('View our service packages');
    }

    // General suggestions
    if (actions.length === 0) {
      actions.push('Book a free consultation');
      actions.push('Learn about our services');
      actions.push('Contact our team');
    }

    return actions.slice(0, 2); // Limit to 2 suggestions
  }

  // Generate fallback response when no knowledge is found
  private generateFallbackResponse(context: AIResponseContext): AIResponse {
    const fallbackResponses = [
      "That's a great question! While I don't have specific information about that, our team would be happy to help. Would you like to schedule a free consultation?",
      "I'd love to help you with that! For detailed information, I recommend speaking with our team directly. You can book a free 30-minute consultation.",
      "Thanks for asking! For the most accurate and detailed answer, let me connect you with our experts. Would you like to schedule a call?"
    ];

    return {
      content: fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)],
      confidence: 0.2,
      sources: [],
      suggestedActions: ['Book a free consultation', 'Contact our team', 'Learn about our services']
    };
  }

  // Initialize knowledge base with website scraping
  async initializeKnowledgeBase(): Promise<void> {
    const urlsToScrape = [
      'https://upzera.com/en',
      'https://upzera.com/en/about',
      'https://upzera.com/en/our-approach',
      'https://upzera.com/en/testimonials',
      'https://upzera.com/en/faq',
      'https://upzera.com/en/contact',
      'https://upzera.com/en/website-development',
      'https://upzera.com/en/chatbot-integration'
    ];

    try {
      console.log('🚀 Initializing knowledge base...');

      // For client-side, we'll use in-memory caching only
      // File caching is handled by the API endpoints
      await this.knowledgeScraper.buildKnowledgeBase(urlsToScrape);

      console.log('✅ Knowledge base initialized successfully');
      console.log('📊 Stats:', this.knowledgeScraper.getStats());
    } catch (error) {
      console.warn('⚠️ Web scraping failed, using static knowledge only:', error);
      // Fallback to static knowledge only - this is perfectly fine
      await this.knowledgeScraper.buildKnowledgeBase([]);
      console.log('📚 Static knowledge base loaded as fallback');
    }
  }

  // Get knowledge base statistics
  getKnowledgeStats() {
    return this.knowledgeScraper.getStats();
  }

  // Refresh knowledge base (can be called periodically)
  async refreshKnowledgeBase(force: boolean = false): Promise<void> {
    console.log('Refreshing knowledge base...');

    const urlsToScrape = [
      'https://upzera.com/en',
      'https://upzera.com/en/about',
      'https://upzera.com/en/our-approach',
      'https://upzera.com/en/testimonials',
      'https://upzera.com/en/faq',
      'https://upzera.com/en/contact',
      'https://upzera.com/en/website-development',
      'https://upzera.com/en/chatbot-integration'
    ];

    if (force) {
      await this.knowledgeScraper.forceRefresh(urlsToScrape);
    } else {
      await this.knowledgeScraper.buildKnowledgeBase(urlsToScrape);
    }
  }

  // Search knowledge directly (for debugging/testing)
  searchKnowledge(query: string, limit: number = 5): KnowledgeChunk[] {
    return this.knowledgeScraper.searchKnowledge(query, limit);
  }
}

// Utility function to create enhanced responder instance
export function createEnhancedResponder(apiKey: string): EnhancedAIResponder {
  return new EnhancedAIResponder(apiKey);
}

// Types are already exported as interfaces above
